//
//  Localized.strings
//  TUICallKit
//
//  Created by noah on 2023/12/4.
//  Copyright © 2023 Tencent. All rights reserved.
//

"TUICallKit.inviteToVideoCall" = "邀请你视频通话";
"TUICallKit.inviteToAudioCall" = "邀请你语音通话";
"TUICallKit.waitAccept" = "等待对方接受邀请";
"TUICallKit.accept" = "已接通";
"TUICallKit.Group.inviteToGroupCall" = "邀请你加入多人通话";
"TUICallKit.Group.waitAccept" = "等待接听";
"TUICallKit.determine" = "确定";
"TUICallKit.lineBusy" = "对方忙";
"TUICallKit.userHangup" = "\%@ 结束通话";
"TUICallKit.userReject" = "\%@ 拒绝了通话请求";
"TUICallKit.otherPartyHangup" = "对方已挂断，通话结束";
"TUICallKit.otherPartyReject" = "对方拒绝了通话请求";
"TUICallKit.otherPartyNoResponse" = "对方未响应";

"TUICallKit.switchCamera" = "翻转";
"TUICallKit.blurBackground" = "模糊背景";
"TUICallKit.hangup" = "挂断";
"TUICallKit.decline" = "拒接";
"TUICallKit.switchToAudio" = "切换到语音通话";
"TUICallKit.answer" = "接听";
"TUICallKit.yournetworkpoor" = "己方网络不佳";
"TUICallKit.calleeTip" = "参与通话的还有：";
"TUICallKit.FloatingWindow.waitAccept" = "等待接听";

"TUICallKit.User.Exceed.Limit" = "暂支持最多9人通话。如需多人会议，请使用TUIRoom";
"TUICallKit.UnableToRestartTheCall" = "正在通话中，无法再次发起";

"TUICallKit.purchased" = "您当前未购买音视频通话能力套餐，请前往IM 控制台开通免费体验或加购正式版";
"TUICallKit.support" = "您当前购买的音视频通话能力套餐不支持该功能，建议您升级套餐类型";
"TUICallKit.ErrorInPeerBlacklist" = "发起通话失败，用户在黑名单中，禁止发起！";
"TUICallKit.ErrorInvalidLogin" = "登录失败,请重新登录";
"TUICallKit.ErrorParameterInvalid" = "参数错误";
"TUICallKit.ErrorRequestRefused" = "当前状态不支持调用";
"TUICallKit.ErrorRequestRepeated" = "当前方法正在执行中，请勿重复调用";
"TUICallKit.ErrorSceneNotSupport" = "当前通话场景，不支持该功能";

"TUICallKit.FailedToGetCameraPermission.Title" = "相机权限未开启";
"TUICallKit.FailedToGetCameraPermission.Tips"  = "无法使用视频通话功能，点击\"去开启\"打开相机权限";
"TUICallKit.FailedToGetCameraPermission.Later" = "以后再说";
"TUICallKit.FailedToGetCameraPermission.Enable" = "去开启";

"TUICallKit.FailedToGetMicrophonePermission.Title" = "麦克风权限未开启";
"TUICallKit.FailedToGetMicrophonePermission.Tips"  = "无法使用发送音视频通话功能，点击\"去开启\"打开麦克风权限";
"TUICallKit.FailedToGetMicrophonePermission.Later" = "以后再说";
"TUICallKit.FailedToGetMicrophonePermission.Enable" = "去开启";

"TUICallKit.have.new.invitation" = "您有一个新的通话";

"TUICallKit.Recents.all" = "所有通话";
"TUICallKit.Recents.missed" = "未接通话";
"TUICallKit.Recents.calls" = "通话记录";
"TUICallKit.Recents.clear" = "清除";
"TUICallKit.Recents.done" = "完成";
"TUICallKit.Recents.delete" = "删除";
"TUICallKit.Recents.addUser" = "添加成员";
"TUICallKit.Recents.clear.all" = "清除所有记录";
"TUICallKit.Recents.clear.cancel" = "取消";
"TUICallKit.Recents.incoming" = "来电";
"TUICallKit.Recents.outgoing" = "去电";

"TUICallKit.unmuted" = "麦克风已开";
"TUICallKit.muted" = "麦克风已关";
"TUICallKit.speakerPhone" = "扬声器已开";
"TUICallKit.earpiece" = "扬声器已关";
"TUICallKit.cameraOn" = "摄像头已开";
"TUICallKit.cameraOff" = "摄像头已关";

"TUICallKit.JoinGroupView.title" = "\%d人正在\%@";
"TUICallKit.JoinGroupView.join" = "加入";
"TUICallKit.JoinGroupView.audioCall" = "语音通话";
"TUICallKit.JoinGroupView.videoCall" = "视频通话";

"TUICallKit.OtherParty.NetworkLowQuality" = "当前通话对方网络不佳";
"TUICallKit.Self.NetworkLowQuality" = "当前通话你的网络不佳";
