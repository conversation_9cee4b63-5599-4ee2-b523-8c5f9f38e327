//
//  Localized.strings
//  TUICallKit
//
//  Created by noah on 2023/12/4.
//  Copyright © 2023 Tencent. All rights reserved.
//

"TUICallKit.inviteToVideoCall" = "invites you to a video call";
"TUICallKit.inviteToAudioCall" = "invites you to a voice call";
"TUICallKit.waitAccept" = "Awaiting response";
"TUICallKit.accept" = "Connected";
"TUICallKit.Group.inviteToGroupCall" = "invites you to a group call";
"TUICallKit.Group.waitAccept" = "Waiting";
"TUICallKit.determine" = "OK";
"TUICallKit.lineBusy" = "Line Busy";
"TUICallKit.userHangup" = "\%@ end the call.";
"TUICallKit.userReject" = "\%@ rejected call";
"TUICallKit.otherPartyHangup" = "Other party hung up, call ended";
"TUICallKit.otherPartyReject" = "Call rejected by other party";
"TUICallKit.otherPartyNoResponse" = "The other party did not respond";

"TUICallKit.switchCamera" = "Switch";
"TUICallKit.blurBackground" = "Blur Background";
"TUICallKit.hangup" = "Hang Up";
"TUICallKit.decline" = "Decline";
"TUICallKit.switchToAudio" = "Switch to Voice Call";
"TUICallKit.answer" = "Answer";
"TUICallKit.calleeTip" = "Also on the call:";
"TUICallKit.FloatingWindow.waitAccept" = "Waiting";

"TUICallKit.User.Exceed.Limit" = "TUICalling currently supports call with up to 9 people.For larger conference calls,try using TUIRoom";
"TUICallKit.UnableToRestartTheCall" = "Unable to restart the call";

"TUICallKit.purchased" = "You do not have TUICallKit package, please open the free experience in the console or purchase the official package";
"TUICallKit.support" = "The package you purchased does not support this ability";
"TUICallKit.ErrorInPeerBlacklist" = "The identifier is in blacklist. Failed to send this message!";
"TUICallKit.ErrorInvalidLogin" = "Invalid login, please login again";
"TUICallKit.ErrorParameterInvalid" = "Parameter error";
"TUICallKit.ErrorRequestRefused" = "The current status can not use this function";
"TUICallKit.ErrorRequestRepeated" = "The current status is waiting/accept, please do not call it repeatedly";
"TUICallKit.ErrorSceneNotSupport" = "The current scene does not support this function";

"TUICallKit.FailedToGetCameraPermission.Title" = "No access to camera";
"TUICallKit.FailedToGetCameraPermission.Tips"  = "Unable to use the video call function, click \"Authorize Now\" to open the camera permission.";
"TUICallKit.FailedToGetCameraPermission.Later" = "Later";
"TUICallKit.FailedToGetCameraPermission.Enable" = "Authorize Now";

"TUICallKit.FailedToGetMicrophonePermission.Title" = "No access to microphone";
"TUICallKit.FailedToGetMicrophonePermission.Tips"  = "Unable to use the function of sending audio and video calls, click \"Authorize Now\" to open the microphone permission";
"TUICallKit.FailedToGetMicrophonePermission.Later" = "Later";
"TUICallKit.FailedToGetMicrophonePermission.Enable" = "Authorize Now";

"TUICallKit.have.new.invitation" = "You have a new call";

"TUICallKit.Recents.all" = "All";
"TUICallKit.Recents.missed" = "Missed";
"TUICallKit.Recents.calls" = "Recent Calls";
"TUICallKit.Recents.clear" = "Clear";
"TUICallKit.Recents.done" = "Done";
"TUICallKit.Recents.delete" = "Delete";
"TUICallKit.Recents.addUser"= "Add User";
"TUICallKit.Recents.clear.all" = "Clear All Call History";
"TUICallKit.Recents.clear.cancel" = "Cancel";
"TUICallKit.Recents.incoming" = "Incoming";
"TUICallKit.Recents.outgoing" = "Outgoing";

"TUICallKit.unmuted" = "Unmuted";
"TUICallKit.muted" = "Muted";
"TUICallKit.speakerPhone" = "Speaker";
"TUICallKit.earpiece" = "Earpiece";
"TUICallKit.cameraOn" = "Camera On";
"TUICallKit.cameraOff" = "Camera Off";

"TUICallKit.JoinGroupView.title" = "%d people are having a %@";
"TUICallKit.JoinGroupView.join" = "Join";
"TUICallKit.JoinGroupView.audioCall" = "Voice Call";
"TUICallKit.JoinGroupView.videoCall" = "Video Call";

"TUICallKit.OtherParty.NetworkLowQuality" = "Other party's network poor";
"TUICallKit.Self.NetworkLowQuality" = "Your network poor";
