//
//  Localized.strings
//  TUICallKit
//
//  Created by noah on 2023/12/4.
//  Copyright © 2023 Tencent. All rights reserved.
//

"TUICallKit.inviteToVideoCall" = "يدعوك لمكالمة فيديو";
"TUICallKit.inviteToAudioCall" = "يدعوك لمكالمة صوتية";
"TUICallKit.waitAccept" = "انتظار قبول الطرف الآخر";
"TUICallKit.accept" = "متصل";
"TUICallKit.Group.inviteToGroupCall" = "يدعوك إلى مكالمة";
"TUICallKit.Group.waitAccept" = "منتظر";
"TUICallKit.determine" = "تأكيد ";
"TUICallKit.lineBusy" = "الخط مشغول";
"TUICallKit.userHangup" = "\%@ انهى المكالمة";
"TUICallKit.userReject" = "\%@ رفض المكالمة";
"TUICallKit.otherPartyHangup" = "أنهى الطرف الآخر المكالمة";
"TUICallKit.otherPartyReject" = "الطرف الآخر رفض المكالمة";
"TUICallKit.otherPartyNoResponse" = "لم يستجب الطرف الآخر";

"TUICallKit.switchCamera" = "يُحوّل";
"TUICallKit.blurBackground" = "الخلفية الغائمة";
"TUICallKit.hangup" = "إنهاء المكالمة";
"TUICallKit.decline" = "رفض";
"TUICallKit.switchToAudio" = "التبديل إلى المكالمة الصوتية";
"TUICallKit.answer" = "الرد";
"TUICallKit.calleeTip" = "أيضًا على المكالمة";
"TUICallKit.FloatingWindow.waitAccept" = "في انتظار الرد";

"TUICallKit.User.Exceed.Limit" = "يدعم مكالمات مؤقتة لما يصل إلى 9 أشخاص. لاستخدام المؤتمرات متعددة الأطراف ، يرجى استخدام TUIRoom";
"TUICallKit.UnableToRestartTheCall" = "لا يمكن إعادة بدء المكالمة أثناء المكالمة";

"TUICallKit.purchased" = "لم تشترِ حزمة قدرات المكالمات الصوتية والمرئية ، يرجى الانتقال إلى لوحة تحكم IM لتجربة مجانية أو شراء الإصدار الرسمي";
"TUICallKit.support" = "لا يدعم حزمة قدرات المكالمات الصوتية والمرئية التي اشتريتها حاليًا هذه الميزة ، نوصي بترقية نوع الحزمة";
"TUICallKit.ErrorInPeerBlacklist" = "المعرف في القائمة السوداء. فشل في إرسال هذه الرسالة!";
"TUICallKit.ErrorInvalidLogin" = "الرجاء تسجيل الدخول مرة أخرى، تسجيل الدخول غير صحيح";
"TUICallKit.ErrorParameterInvalid" = "خطأ المعلمة";
"TUICallKit.ErrorRequestRefused" = "الحالة الحالية لا تدعم نداء الدالة";
"TUICallKit.ErrorRequestRepeated" = "الطريقة الحالية تتم تنفيذها، يرجى عدم استدعاءها مرة أخرى";
"TUICallKit.ErrorSceneNotSupport" = "مشهد المكالمة الحالي لا يدعم هذه الوظيفة";

"TUICallKit.FailedToGetCameraPermission.Title" = "لم يتم تمكين إذن الكاميرا";
"TUICallKit.FailedToGetCameraPermission.Tips"  = "لا يمكن استخدام وظيفة المكالمة المرئية ، انقر فوق \"فتح\" لفتح إذن الكاميرا";
"TUICallKit.FailedToGetCameraPermission.Later" = "قل ذلك لاحقًا";
"TUICallKit.FailedToGetCameraPermission.Enable" = "فتح";

"TUICallKit.FailedToGetMicrophonePermission.Title" = "لم يتم تمكين إذن الميكروفون";
"TUICallKit.FailedToGetMicrophonePermission.Tips"  = "لا يمكن استخدام وظيفة إرسال المكالمات الصوتية والمرئية ، انقر فوق \"فتح\" لفتح إذن الميكروفون";
"TUICallKit.FailedToGetMicrophonePermission.Later" = "قل ذلك لاحقًا";
"TUICallKit.FailedToGetMicrophonePermission.Enable" = "فتح";

"TUICallKit.have.new.invitation" = "لديك مكالمة جديدة";

"TUICallKit.Recents.all" = "جميع المكالمات";
"TUICallKit.Recents.missed" = "المكالمات الفائتة";
"TUICallKit.Recents.calls" = "سجل المكالمات";
"TUICallKit.Recents.clear" = "مسح";
"TUICallKit.Recents.done" = "تم";
"TUICallKit.Recents.delete" = "حذف";
"TUICallKit.Recents.addUser" = "إضافة عضو";
"TUICallKit.Recents.clear.all" = "مسح جميع السجلات";
"TUICallKit.Recents.clear.cancel" = "إلغاء";
"TUICallKit.Recents.incoming" = "وارد";
"TUICallKit.Recents.outgoing" = "صادر";

"TUICallKit.unmuted" = "إلغاء الكتم";
"TUICallKit.muted" = "كتم الصوت";
"TUICallKit.speakerPhone" = "مكبر الصوت";
"TUICallKit.earpiece" = "سماعة الأذن";
"TUICallKit.cameraOn" = "تشغيل الكاميرا";
"TUICallKit.cameraOff" = "إيقاف الكاميرا";

"TUICallKit.JoinGroupView.title" = "يرتاد %d أشخاص %@";
"TUICallKit.JoinGroupView.join" = "الانضمام";
"TUICallKit.JoinGroupView.audioCall" = "مكالمة صوتية";
"TUICallKit.JoinGroupView.videoCall" = "مكالمة فيديو";

"TUICallKit.OtherParty.NetworkLowQuality" = "شبكة الطرف الآخر ضعيفة";
"TUICallKit.Self.NetworkLowQuality" = "شبكتك ضعيفة في المكالمة";
