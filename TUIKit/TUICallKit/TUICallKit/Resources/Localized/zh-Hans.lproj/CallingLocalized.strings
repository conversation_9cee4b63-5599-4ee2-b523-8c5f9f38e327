/*
 CallingLocalized.strings
 TRTCCalling
 
 Created by adams on 2021/5/13.
 */

"Demo.TRTC.calling.callingrefuse" = "拒绝了通话";
"Demo.TRTC.calling.callingleave" = "离开了通话";
"Demo.TRTC.calling.callingnoresponse" = "未响应";
"Demo.TRTC.calling.callingbusy" = "忙线";
"Demo.TRTC.calling.invitetovideocall" = "邀请你视频通话";
"Demo.TRTC.calling.invitetoaudiocall" = "邀请你音频通话";
"LoginNetwork.AppUtils.determine" = "确定";
"TUICallKit.Audio.call" = "语音通话";
"TUICallKit.Video.call" = "视频通话";

"Demo.TRTC.Calling.waitaccept" = "等待对方接受";
"Demo.TRTC.Calling.hangup" = "挂断";
"Demo.TRTC.Calling.decline" = "拒接";
"Demo.TRTC.Calling.mic" = "麦克风";
"Demo.TRTC.Calling.speaker" = "扬声器";
"Demo.TRTC.Calling.camera" = "摄像头";
"Demo.TRTC.Calling.switchtoaudio" = "切换到语音通话";
"Demo.TRTC.Calling.answer" = "接听";
"Demo.TRTC.Calling.othernetworkpoor" = "对方网络不佳";
"Demo.TRTC.Calling.yournetworkpoor" = "己方网络不佳";
"Demo.TRTC.Salon.invitelimited" = "请求正在处理，请稍后再试";
"Demo.TRTC.Calling.calleeTip" = "他们也在";
"Demo.TRTC.Calling.FloatingWindow.waitaccept" = "等待接听";
"Demo.TRTC.Calling.FloatingWindow.dailing" = "呼叫中…";

"Demo.TRTC.Calling.User.Exceed.Limit" = "暂支持最多9人通话。如需多人会议，请使用TUIRoom";
"Demo.TRTC.Calling.UnableToRestartTheCall" = "正在通话中，无法再次发起";
"Demo.TRTC.Calling.ErrorInPeerBlacklist" = "发起通话失败，用户在黑名单中，禁止发起！";

"TUICallKit.ErrorInvalidLogin" = "登录失败,请重新登录";
"TUICallKit.ErrorParameterInvalid" = "参数错误";
"TUICallKit.ErrorRequestRefused" = "当前状态不支持调用";
"TUICallKit.ErrorRequestRepeated" = "当前方法正在执行中，请勿重复调用";
"TUICallKit.ErrorSceneNotSupport" = "当前通话场景，不支持该功能";

"TUICallKit.failedtogetcamerapermission.Title" = "相机权限未开启";
"TUICallKit.failedtogetcamerapermission.Tips"  = "无法使用视频通话功能，点击\"去开启\"打开相机权限";
"TUICallKit.failedtogetcamerapermission.Later" = "以后再说";
"TUICallKit.failedtogetcamerapermission.Enable" = "去开启";

"TUICallKit.failedtogetmicrophonepermission.Title" = "麦克风权限未开启";
"TUICallKit.failedtogetmicrophonepermission.Tips"  = "无法使用发送音视频通话功能，点击\"去开启\"打开麦克风权限";
"TUICallKit.failedtogetmicrophonepermission.Later" = "以后再说";
"TUICallKit.failedtogetmicrophonepermission.Enable" = "去开启";

"TUICallKit.package.not.purchased" = "您当前未购买音视频通话能力套餐，请前往IM 控制台开通免费体验或加购正式版";
"TUICallKit.package.not.support" = "您当前购买的音视频通话能力套餐不支持该功能，建议您升级套餐类型";
"TUICallKit.have.new.invitation" = "您有一个新的通话";

"TUICallKit.Recents.all" = "所有通话";
"TUICallKit.Recents.missed" = "未接通话";
"TUICallKit.Recents.calls" = "通话记录";
"TUICallKit.Recents.clear" = "清除";
"TUICallKit.Recents.done" = "完成";
"TUICallKit.Recents.delete" = "删除";
"TUICallKit.Recents.addUser" = "添加成员";
"TUICallKit.Recents.clear.all" = "清除所有记录";
"TUICallKit.Recents.clear.cancel" = "取消";
"TUICallKit.Recents.incoming" = "来电";
"TUICallKit.Recents.outgoing" = "去电";
