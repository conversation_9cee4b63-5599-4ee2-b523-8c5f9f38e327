/* 
 CallingLocalized.strings
 TRTCCalling
 
 Created by adams on 2021/5/13.
 */

"Demo.TRTC.calling.callingrefuse" = "declined the call";
"Demo.TRTC.calling.callingleave" = "left the call";
"Demo.TRTC.calling.callingnoresponse" = "didn’t answer";
"Demo.TRTC.calling.callingbusy" = "is busy";
"Demo.TRTC.calling.invitetovideocall" = "invites you to a video call";
"Demo.TRTC.calling.invitetoaudiocall" = "invites you to a voice call";
"LoginNetwork.AppUtils.determine" = "OK";
"TUICallKit.Audio.call" = "Voice Call";
"TUICallKit.Video.call" = "Video Call";

"Demo.TRTC.Calling.waitaccept" = "Waiting for the user to accept...";
"Demo.TRTC.Calling.hangup" = "Hang Up";
"Demo.TRTC.Calling.decline" = "Decline";
"Demo.TRTC.Calling.mic" = "Mic";
"Demo.TRTC.Calling.speaker" = "Speaker";
"Demo.TRTC.Calling.camera" = "Camera";
"Demo.TRTC.Calling.switchtoaudio" = "Switch to Voice Call";
"Demo.TRTC.Calling.answer" = "Answer";
"Demo.TRTC.Salon.invitelimited" = "The request is being processed, please try again later";
"Demo.TRTC.Calling.calleeTip" = "They also";
"Demo.TRTC.Calling.FloatingWindow.waitaccept" = "Waiting";
"Demo.TRTC.Calling.FloatingWindow.dailing" = "Waiting";

"Demo.TRTC.Calling.User.Exceed.Limit" = "TUICalling currently supports call with up to 9 people.For larger conference calls,try using TUIRoom";
"Demo.TRTC.Calling.UnableToRestartTheCall" = "Unable to restart the call";
"Demo.TRTC.Calling.ErrorInPeerBlacklist" = "The identifier is in blacklist. Failed to send this message!";

"TUICallKit.ErrorInvalidLogin" = "Invalid login, please login again";
"TUICallKit.ErrorParameterInvalid" = "Parameter error";
"TUICallKit.ErrorRequestRefused" = "The current status can not use this function";
"TUICallKit.ErrorRequestRepeated" = "The current status is waiting/accept, please do not call it repeatedly";
"TUICallKit.ErrorSceneNotSupport" = "The current scene does not support this function";

"TUICallKit.failedtogetcamerapermission.Title" = "No access to camera";
"TUICallKit.failedtogetcamerapermission.Tips"  = "Unable to use the video call function, click \"Authorize Now\" to open the camera permission.";
"TUICallKit.failedtogetcamerapermission.Later" = "Later";
"TUICallKit.failedtogetcamerapermission.Enable" = "Authorize Now";

"TUICallKit.failedtogetmicrophonepermission.Title" = "No access to microphone";
"TUICallKit.failedtogetmicrophonepermission.Tips"  = "Unable to use the function of sending audio and video calls, click \"Authorize Now\" to open the microphone permission";
"TUICallKit.failedtogetmicrophonepermission.Later" = "Later";
"TUICallKit.failedtogetmicrophonepermission.Enable" = "Authorize Now";

"TUICallKit.package.not.purchased" = "You do not have TUICallKit package, please open the free experience in the console or purchase the official package";
"TUICallKit.package.not.support" = "The package you purchased does not support this ability";
"TUICallKit.have.new.invitation" = "You have a new call";

"TUICallKit.Recents.all" = "All";
"TUICallKit.Recents.missed" = "Missed";
"TUICallKit.Recents.calls" = "Recent Calls";
"TUICallKit.Recents.clear" = "Clear";
"TUICallKit.Recents.done" = "Done";
"TUICallKit.Recents.delete" = "Delete";
"TUICallKit.Recents.addUser"= "Add User";
"TUICallKit.Recents.clear.all" = "Clear All Call History";
"TUICallKit.Recents.clear.cancel" = "Cancel";
"TUICallKit.Recents.incoming" = "Incoming";
"TUICallKit.Recents.outgoing" = "Outgoing";
