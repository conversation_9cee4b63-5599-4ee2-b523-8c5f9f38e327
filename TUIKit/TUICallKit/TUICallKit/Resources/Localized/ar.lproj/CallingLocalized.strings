/* 
 CallingLocalized.strings
 TRTCCalling
 
 Created by noah on 2023/8/3.
 */

"Demo.TRTC.calling.callingrefuse" = "رفض المكالمة";
"Demo.TRTC.calling.callingleave" = "مغادرة المكالمة";
"Demo.TRTC.calling.callingnoresponse" = "لا يوجد استجابة";
"Demo.TRTC.calling.callingbusy" = "خط مشغول";
"Demo.TRTC.calling.invitetovideocall" = "يدعوك لمكالمة فيديو";
"Demo.TRTC.calling.invitetoaudiocall" = "يدعوك لمكالمة صوتية";
"LoginNetwork.AppUtils.determine" = "تأكيد";
"TUICallKit.Audio.call" = "مكالمة صوتية";
"TUICallKit.Video.call" = "مكالمة فيديو";

"Demo.TRTC.Calling.waitaccept" = "انتظار قبول الطرف الآخر";
"Demo.TRTC.Calling.hangup" = "إنهاء المكالمة";
"Demo.TRTC.Calling.decline" = "رفض";
"Demo.TRTC.Calling.mic" = "ميكروفون";
"Demo.TRTC.Calling.speaker" = "مكبر الصوت";
"Demo.TRTC.Calling.camera" = "كاميرا";
"Demo.TRTC.Calling.switchtoaudio" = "التبديل إلى المكالمة الصوتية";
"Demo.TRTC.Calling.answer" = "الرد";
"Demo.TRTC.Salon.invitelimited" = "الطلب قيد المعالجة ، يرجى المحاولة مرة أخرى لاحقًا";
"Demo.TRTC.Calling.calleeTip" = "هم أيضا هنا";
"Demo.TRTC.Calling.FloatingWindow.waitaccept" = "في انتظار الرد";
"Demo.TRTC.Calling.FloatingWindow.dailing" = "الاتصال ...";

"Demo.TRTC.Calling.User.Exceed.Limit" = "يدعم مكالمات مؤقتة لما يصل إلى 9 أشخاص. لاستخدام المؤتمرات متعددة الأطراف ، يرجى استخدام TUIRoom";
"Demo.TRTC.Calling.UnableToRestartTheCall" = "لا يمكن إعادة بدء المكالمة أثناء المكالمة";
"Demo.TRTC.Calling.ErrorInPeerBlacklist" = "المعرف في القائمة السوداء. فشل في إرسال هذه الرسالة!";

"TUICallKit.ErrorInvalidLogin" = "الرجاء تسجيل الدخول مرة أخرى، تسجيل الدخول غير صحيح";
"TUICallKit.ErrorParameterInvalid" = "خطأ المعلمة";
"TUICallKit.ErrorRequestRefused" = "الحالة الحالية لا تدعم نداء الدالة";
"TUICallKit.ErrorRequestRepeated" = "الطريقة الحالية تتم تنفيذها، يرجى عدم استدعاءها مرة أخرى";
"TUICallKit.ErrorSceneNotSupport" = "مشهد المكالمة الحالي لا يدعم هذه الوظيفة";

"TUICallKit.failedtogetcamerapermission.Title" = "لم يتم تمكين إذن الكاميرا";
"TUICallKit.failedtogetcamerapermission.Tips"  = "لا يمكن استخدام وظيفة المكالمة المرئية ، انقر فوق \"فتح\" لفتح إذن الكاميرا";
"TUICallKit.failedtogetcamerapermission.Later" = "قل ذلك لاحقًا";
"TUICallKit.failedtogetcamerapermission.Enable" = "فتح";

"TUICallKit.failedtogetmicrophonepermission.Title" = "لم يتم تمكين إذن الميكروفون";
"TUICallKit.failedtogetmicrophonepermission.Tips"  = "لا يمكن استخدام وظيفة إرسال المكالمات الصوتية والمرئية ، انقر فوق \"فتح\" لفتح إذن الميكروفون";
"TUICallKit.failedtogetmicrophonepermission.Later" = "قل ذلك لاحقًا";
"TUICallKit.failedtogetmicrophonepermission.Enable" = "فتح";

"TUICallKit.package.not.purchased" = "لم تشترِ حزمة قدرات المكالمات الصوتية والمرئية ، يرجى الانتقال إلى لوحة تحكم IM لتجربة مجانية أو شراء الإصدار الرسمي";
"TUICallKit.package.not.support" = "لا يدعم حزمة قدرات المكالمات الصوتية والمرئية التي اشتريتها حاليًا هذه الميزة ، نوصي بترقية نوع الحزمة";
"TUICallKit.have.new.invitation" = "لديك مكالمة جديدة";

"TUICallKit.Recents.all" = "جميع المكالمات";
"TUICallKit.Recents.missed" = "المكالمات الفائتة";
"TUICallKit.Recents.calls" = "سجل المكالمات";
"TUICallKit.Recents.clear" = "مسح";
"TUICallKit.Recents.done" = "تم";
"TUICallKit.Recents.delete" = "حذف";
"TUICallKit.Recents.addUser" = "إضافة عضو";
"TUICallKit.Recents.clear.all" = "مسح جميع السجلات";
"TUICallKit.Recents.clear.cancel" = "إلغاء";
"TUICallKit.Recents.incoming" = "وارد";
"TUICallKit.Recents.outgoing" = "صادر";
